'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  <PERSON>r<PERSON><PERSON><PERSON>, 
  UserX,
  <PERSON>fresh<PERSON><PERSON>,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from '@/hooks/use-toast'
import { usePagination } from '@/hooks/use-pagination'
import { RBACService } from '@/lib/services/rbac.service'
import { LpkMitraService } from '@/lib/services'
import { useMinimalAuth } from '@/lib/auth/minimal-auth-context'
import { PermissionGuard } from '@/components/auth/permission-guard'
import type { UserProfile, Role, UserFilterParams } from '@/lib/types/rbac'
import type { LpkMitra } from '@/lib/types/database'
import { UserFormModal } from './components/user-form-modal'

function UsersPage() {
  const [users, setUsers] = useState<UserProfile[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [lpkMitras, setLpkMitras] = useState<LpkMitra[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  
  // Modal states
  const [isFormModalOpen, setIsFormModalOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<UserProfile | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deletingUser, setDeletingUser] = useState<UserProfile | null>(null)
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<string>('')
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const [selectedLpk, setSelectedLpk] = useState<string>('')
  
  const { currentPage, itemsPerPage, totalPages, totalItems, handlePageChange, updatePagination } = usePagination()

  const fetchData = async () => {
    try {
      setRefreshing(true)
      
      const filters: UserFilterParams = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined,
        role: selectedRole || undefined,
        is_active: selectedStatus === 'active' ? true : selectedStatus === 'inactive' ? false : undefined,
        lpk_mitra_id: selectedLpk || undefined
      }
      
      const [usersResponse, rolesResponse, lpkResponse] = await Promise.all([
        RBACService.getUsers(filters),
        RBACService.getRoles({ is_active: true }),
        LpkMitraService.getLpkMitra({ is_active: true })
      ])
      
      setUsers(usersResponse.data)
      updatePagination(usersResponse.total, usersResponse.page, usersResponse.limit)
      
      setRoles(rolesResponse.data)
      setLpkMitras(lpkResponse.data)
      
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data users',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, itemsPerPage, searchTerm, selectedRole, selectedStatus, selectedLpk])

  const handleOpenModal = (user?: UserProfile) => {
    setEditingUser(user || null)
    setIsFormModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsFormModalOpen(false)
    setEditingUser(null)
  }

  const handleSaveUser = async () => {
    await fetchData()
    handleCloseModal()
    toast({
      title: 'Success',
      description: editingUser ? 'User berhasil diupdate' : 'User berhasil ditambahkan'
    })
  }

  const handleDeleteUser = async () => {
    if (!deletingUser) return
    
    try {
      await RBACService.deleteUser(deletingUser.id)
      await fetchData()
      toast({
        title: 'Success',
        description: 'User berhasil dihapus'
      })
    } catch (error) {
      console.error('Error deleting user:', error)
      toast({
        title: 'Error',
        description: 'Gagal menghapus user',
        variant: 'destructive'
      })
    } finally {
      setIsDeleteDialogOpen(false)
      setDeletingUser(null)
    }
  }

  const handleToggleUserStatus = async (user: UserProfile) => {
    try {
      await RBACService.updateUser(user.id, { is_active: !user.is_active })
      await fetchData()
      toast({
        title: 'Success',
        description: `User berhasil ${user.is_active ? 'dinonaktifkan' : 'diaktifkan'}`
      })
    } catch (error) {
      console.error('Error toggling user status:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengubah status user',
        variant: 'destructive'
      })
    }
  }

  const getRoleNames = (user: UserProfile): string => {
    return user.role_assignments
      ?.filter(ra => ra.is_active)
      .map(ra => ra.role?.display_name)
      .filter(Boolean)
      .join(', ') || 'No Role'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Kelola pengguna sistem dan hak akses mereka
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => fetchData()}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
          <PermissionGuard module="users" action="create">
            <Button onClick={() => handleOpenModal()} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Tambah User
            </Button>
          </PermissionGuard>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filter & Pencarian</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari nama, username, email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedRole} onValueChange={setSelectedRole}>
              <SelectTrigger>
                <SelectValue placeholder="Semua Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Role</SelectItem>
                {roles.map((role) => (
                  <SelectItem key={role.id} value={role.name}>
                    {role.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Semua Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Status</SelectItem>
                <SelectItem value="active">Aktif</SelectItem>
                <SelectItem value="inactive">Tidak Aktif</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedLpk} onValueChange={setSelectedLpk}>
              <SelectTrigger>
                <SelectValue placeholder="Semua LPK" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua LPK</SelectItem>
                {lpkMitras.map((lpk) => (
                  <SelectItem key={lpk.id} value={lpk.id}>
                    {lpk.nama_lpk}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Daftar Users ({totalItems})</CardTitle>
          <CardDescription>
            Menampilkan {users.length} dari {totalItems} users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nama</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>LPK Mitra</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.full_name}</div>
                      {user.username && (
                        <div className="text-sm text-gray-500">@{user.username}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{user.email || '-'}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {user.role_assignments?.filter(ra => ra.is_active).map((ra, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {ra.role?.display_name}
                        </Badge>
                      )) || <span className="text-gray-500">No Role</span>}
                    </div>
                  </TableCell>
                  <TableCell>
                    {user.lpk_mitra?.nama_lpk || '-'}
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.is_active ? "default" : "secondary"}>
                      {user.is_active ? 'Aktif' : 'Tidak Aktif'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.last_login 
                      ? new Date(user.last_login).toLocaleDateString('id-ID')
                      : 'Belum pernah login'
                    }
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/users/${user.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            Lihat Detail
                          </Link>
                        </DropdownMenuItem>
                        <PermissionGuard module="users" action="update">
                          <DropdownMenuItem onClick={() => handleOpenModal(user)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleUserStatus(user)}>
                            {user.is_active ? (
                              <>
                                <UserX className="h-4 w-4 mr-2" />
                                Nonaktifkan
                              </>
                            ) : (
                              <>
                                <UserCheck className="h-4 w-4 mr-2" />
                                Aktifkan
                              </>
                            )}
                          </DropdownMenuItem>
                        </PermissionGuard>
                        <DropdownMenuSeparator />
                        <PermissionGuard module="users" action="delete">
                          <DropdownMenuItem 
                            onClick={() => {
                              setDeletingUser(user)
                              setIsDeleteDialogOpen(true)
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Hapus
                          </DropdownMenuItem>
                        </PermissionGuard>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Modals */}
      <UserFormModal
        isOpen={isFormModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveUser}
        user={editingUser}
        roles={roles}
        lpkMitras={lpkMitras}
      />

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus User</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus user "{deletingUser?.full_name}"? 
              Tindakan ini akan menonaktifkan user dan tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser} className="bg-red-600 hover:bg-red-700">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

function UsersPageWithAuth() {
  const { user, loading } = useMinimalAuth()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (!user) {
    if (typeof window !== 'undefined') {
      window.location.href = '/login'
    }
    return null
  }

  return <UsersPage />
}

export default UsersPageWithAuth
