'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

export default function ForceRedirectPage() {
  const router = useRouter()
  const [countdown, setCountdown] = useState(3)
  const [method, setMethod] = useState<string>('')

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          // Try different redirect methods
          testRedirect()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const testRedirect = () => {
    console.log('Testing redirect methods...')
    
    // Method 1: router.push
    setMethod('router.push')
    router.push('/login')
    
    // Method 2: window.location.href (fallback after 1 second)
    setTimeout(() => {
      if (window.location.pathname !== '/login') {
        console.log('router.push failed, trying window.location.href')
        setMethod('window.location.href')
        window.location.href = '/login'
      }
    }, 1000)
    
    // Method 3: window.location.replace (fallback after 2 seconds)
    setTimeout(() => {
      if (window.location.pathname !== '/login') {
        console.log('window.location.href failed, trying window.location.replace')
        setMethod('window.location.replace')
        window.location.replace('/login')
      }
    }, 2000)
  }

  const manualRedirect = (redirectMethod: string) => {
    console.log(`Manual redirect using: ${redirectMethod}`)
    setMethod(redirectMethod)
    
    switch (redirectMethod) {
      case 'router.push':
        router.push('/login')
        break
      case 'router.replace':
        router.replace('/login')
        break
      case 'window.location.href':
        window.location.href = '/login'
        break
      case 'window.location.replace':
        window.location.replace('/login')
        break
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Force Redirect Test</h1>
        
        {countdown > 0 ? (
          <div className="text-center">
            <div className="text-6xl font-bold text-blue-500 mb-4">{countdown}</div>
            <p className="text-gray-600">Auto redirect to login in {countdown} seconds...</p>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-lg font-semibold mb-4">
              Attempting redirect using: <span className="text-blue-500">{method}</span>
            </div>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          </div>
        )}

        <div className="mt-8 space-y-2">
          <h3 className="font-semibold text-gray-700">Manual Test:</h3>
          <button
            onClick={() => manualRedirect('router.push')}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm"
          >
            Test router.push('/login')
          </button>
          <button
            onClick={() => manualRedirect('router.replace')}
            className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 text-sm"
          >
            Test router.replace('/login')
          </button>
          <button
            onClick={() => manualRedirect('window.location.href')}
            className="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 text-sm"
          >
            Test window.location.href = '/login'
          </button>
          <button
            onClick={() => manualRedirect('window.location.replace')}
            className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 text-sm"
          >
            Test window.location.replace('/login')
          </button>
        </div>

        <div className="mt-6 text-center">
          <a 
            href="/login" 
            className="text-blue-500 hover:text-blue-700 underline"
          >
            Or click here to go to login manually
          </a>
        </div>
      </div>
    </div>
  )
}
