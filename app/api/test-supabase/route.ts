import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Supabase connection...')
    console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
    console.log('Has Anon Key:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
    
    // Test basic connection
    const { data: session, error: sessionError } = await supabase.auth.getSession()
    console.log('Session check:', session ? 'Has session' : 'No session', sessionError?.message)
    
    // Test database connection with a simple query
    const { data: testData, error: dbError } = await supabase
      .from('siswa')
      .select('id')
      .limit(1)
    
    console.log('Database test:', dbError ? `Error: ${dbError.message}` : 'Success')
    
    // Test auth state
    const { data: authData } = await supabase.auth.getUser()
    console.log('Auth user:', authData.user ? `User: ${authData.user.email}` : 'No user')
    
    return NextResponse.json({
      success: true,
      config: {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        anonKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0
      },
      session: {
        exists: !!session?.session,
        error: sessionError?.message || null
      },
      database: {
        connection: dbError ? 'Failed' : 'Success',
        error: dbError?.message || null,
        dataReceived: !!testData
      },
      auth: {
        hasUser: !!authData.user,
        userEmail: authData.user?.email || null
      },
      timestamp: new Date().toISOString()
    })
  } catch (error: any) {
    console.error('Supabase test error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
