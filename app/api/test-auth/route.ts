import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Test Supabase connection
    const { data: session, error: sessionError } = await supabase.auth.getSession()
    
    // Test database connection
    const { data: testData, error: dbError } = await supabase
      .from('siswa')
      .select('count')
      .limit(1)
    
    return NextResponse.json({
      success: true,
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      session: session ? 'Session exists' : 'No session',
      sessionError: sessionError?.message || null,
      dbConnection: dbError ? 'Failed' : 'Success',
      dbError: dbError?.message || null,
      timestamp: new Date().toISOString()
    })
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
