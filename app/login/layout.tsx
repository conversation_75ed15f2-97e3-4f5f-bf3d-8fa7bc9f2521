import type React from "react"
import { Toaster } from "@/components/ui/toaster"
import { SimpleAuthProvider } from "@/lib/auth/simple-auth-context"

export default function LoginLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Login page uses SimpleAuthProvider to avoid complex RBAC loading
  return (
    <SimpleAuthProvider>
      {children}
      <Toaster />
    </SimpleAuthProvider>
  )
}
