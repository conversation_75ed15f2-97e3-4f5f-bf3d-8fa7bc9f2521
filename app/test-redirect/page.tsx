'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

export default function TestRedirectPage() {
  const router = useRouter()
  const [logs, setLogs] = useState<string[]>([])
  const [shouldRedirect, setShouldRedirect] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(`[${timestamp}] ${message}`)
  }

  useEffect(() => {
    addLog('Component mounted')
    
    if (shouldRedirect) {
      addLog('Attempting redirect to /login')
      router.push('/login')
    }
  }, [shouldRedirect, router])

  const testRedirect = () => {
    addLog('Test redirect button clicked')
    setShouldRedirect(true)
  }

  const testDirectRedirect = () => {
    addLog('Testing direct redirect')
    window.location.href = '/login'
  }

  const testRouterPush = () => {
    addLog('Testing router.push')
    router.push('/login')
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Redirect Test Page</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="space-y-4">
            <button
              onClick={testRedirect}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test useEffect Redirect
            </button>
            
            <button
              onClick={testDirectRedirect}
              className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Direct Redirect (window.location.href)
            </button>
            
            <button
              onClick={testRouterPush}
              className="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
            >
              Test Router Push
            </button>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Logs</h2>
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500">No logs yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="text-sm font-mono mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
