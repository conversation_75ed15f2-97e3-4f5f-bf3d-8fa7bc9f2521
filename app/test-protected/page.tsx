'use client'

import { ProtectedRoute } from '@/components/auth/protected-route'

export default function TestProtectedPage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-green-50 border border-green-200 p-6 rounded-lg mb-6">
            <h1 className="text-2xl font-bold text-green-800 mb-2">
              🎉 Success! You are authenticated!
            </h1>
            <p className="text-green-700">
              This page is protected and you can only see it if you're logged in.
              The authentication and redirect system is working correctly.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-green-500">✅</span>
                <span>Authentication context is working</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500">✅</span>
                <span>Protected route component is working</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500">✅</span>
                <span>User session is valid</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500">✅</span>
                <span>Redirect logic is working (you weren't redirected)</span>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow mt-6">
            <h2 className="text-xl font-semibold mb-4">Next Steps</h2>
            <p className="text-gray-600 mb-4">
              Since you can see this page, the authentication system is working correctly.
              The issue with the main dashboard might be with the HOC implementation.
            </p>
            <div className="space-y-2">
              <a 
                href="/login" 
                className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mr-2"
              >
                Go to Login Page
              </a>
              <a 
                href="/" 
                className="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mr-2"
              >
                Try Main Dashboard
              </a>
              <a 
                href="/debug-auth" 
                className="inline-block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
              >
                Debug Authentication
              </a>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
