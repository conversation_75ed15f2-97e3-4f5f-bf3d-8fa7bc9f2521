'use client'

import { useSimpleAuth } from '@/lib/auth/simple-auth-context'

export default function TestSimpleAuthPage() {
  console.log('TestSimpleAuthPage - Component rendering...')
  
  try {
    const authContext = useSimpleAuth()
    console.log('TestSimpleAuthPage - Auth context:', authContext)
    
    const { user, loading, hasPermission, hasRole, signIn, signOut } = authContext
    
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Test Simple Auth</h1>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Auth Context Test</h2>
            
            <div className="space-y-2">
              <p><strong>User:</strong> {user ? user.email : 'null'}</p>
              <p><strong>Loading:</strong> {loading ? 'true' : 'false'}</p>
              <p><strong>Has Permission (test, read):</strong> {hasPermission('test', 'read') ? 'true' : 'false'}</p>
              <p><strong>Has Role (admin):</strong> {hasRole('admin') ? 'true' : 'false'}</p>
            </div>
            
            <div className="mt-6 space-y-2">
              <button
                onClick={() => console.log('signIn function:', typeof signIn)}
                className="block w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Test signIn function
              </button>
              
              <button
                onClick={() => console.log('signOut function:', typeof signOut)}
                className="block w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                Test signOut function
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  } catch (error: any) {
    console.error('TestSimpleAuthPage - Error:', error)
    
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold mb-8 text-red-600">Error in Test Simple Auth</h1>
          
          <div className="bg-red-50 border border-red-200 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-red-800">Error Details</h2>
            <pre className="text-sm text-red-700 whitespace-pre-wrap">
              {error.message}
            </pre>
            <pre className="text-xs text-red-600 mt-4 whitespace-pre-wrap">
              {error.stack}
            </pre>
          </div>
        </div>
      </div>
    )
  }
}
