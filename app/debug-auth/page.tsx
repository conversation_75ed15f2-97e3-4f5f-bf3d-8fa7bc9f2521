'use client'

import { useSimpleAuth } from '@/lib/auth/simple-auth-context'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

export default function DebugAuthPage() {
  const { user, loading, signIn, signOut } = useSimpleAuth()
  const router = useRouter()
  const [authLogs, setAuthLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] ${message}`
    setAuthLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }

  useEffect(() => {
    addLog(`Auth state changed - User: ${user ? user.email : 'null'}, Loading: ${loading}`)
  }, [user, loading])

  const handleLogin = async () => {
    try {
      addLog('Attempting login...')
      await signIn('<EMAIL>', 'admin123')
      addLog('Login successful!')
    } catch (error: any) {
      addLog(`Login failed: ${error.message}`)
    }
  }

  const handleLogout = async () => {
    try {
      addLog('Attempting logout...')
      await signOut()
      addLog('Logout successful!')
    } catch (error: any) {
      addLog(`Logout failed: ${error.message}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Debug Page</h1>
        
        {/* Current Status */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Authentication Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`p-4 rounded-lg ${loading ? 'bg-yellow-100' : 'bg-gray-100'}`}>
              <div className="font-semibold">Loading State</div>
              <div className="text-2xl">{loading ? '⏳' : '✅'}</div>
              <div className="text-sm">{loading ? 'Loading...' : 'Ready'}</div>
            </div>
            
            <div className={`p-4 rounded-lg ${user ? 'bg-green-100' : 'bg-red-100'}`}>
              <div className="font-semibold">User Status</div>
              <div className="text-2xl">{user ? '👤' : '❌'}</div>
              <div className="text-sm">{user ? 'Authenticated' : 'Not Authenticated'}</div>
            </div>
            
            <div className="p-4 rounded-lg bg-blue-100">
              <div className="font-semibold">User Email</div>
              <div className="text-sm font-mono">{user?.email || 'N/A'}</div>
              <div className="text-xs text-gray-600">
                ID: {user?.id?.substring(0, 8) || 'N/A'}...
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={handleLogin}
              disabled={loading || !!user}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:bg-gray-300"
            >
              {user ? 'Already Logged In' : 'Login (Test Credentials)'}
            </button>
            
            <button
              onClick={handleLogout}
              disabled={loading || !user}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:bg-gray-300"
            >
              {!user ? 'Not Logged In' : 'Logout'}
            </button>
            
            <button
              onClick={() => router.push('/login')}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Go to Login Page
            </button>
            
            <button
              onClick={() => router.push('/')}
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
            >
              Go to Dashboard (Protected)
            </button>
          </div>
        </div>

        {/* Logs */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Authentication Logs</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
            {authLogs.length === 0 ? (
              <div className="text-gray-500">No logs yet...</div>
            ) : (
              authLogs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Environment Info */}
        <div className="bg-white p-6 rounded-lg shadow mt-6">
          <h2 className="text-xl font-semibold mb-4">Environment Info</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Supabase URL:</strong>
              <div className="font-mono bg-gray-100 p-2 rounded mt-1">
                {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}
              </div>
            </div>
            <div>
              <strong>Has Anon Key:</strong>
              <div className="font-mono bg-gray-100 p-2 rounded mt-1">
                {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Yes' : 'No'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
