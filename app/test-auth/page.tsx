'use client'

import { useSimpleAuth } from '@/lib/auth/simple-auth-context'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function TestAuthPage() {
  const { user, loading, signIn } = useSimpleAuth()
  const router = useRouter()
  const [debugInfo, setDebugInfo] = useState<any>({})
  const [testResult, setTestResult] = useState<string>('')

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: session, error } = await supabase.auth.getSession()
        
        const info = {
          hasUser: !!user,
          loading,
          sessionExists: !!session?.session,
          sessionError: error?.message,
          supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          currentPath: window.location.pathname,
          userEmail: user?.email || 'No user',
          timestamp: new Date().toISOString()
        }
        
        setDebugInfo(info)
        console.log('Auth Debug Info:', info)
      } catch (err: any) {
        const errorInfo = {
          error: err.message,
          timestamp: new Date().toISOString()
        }
        setDebugInfo(errorInfo)
        console.error('Auth Debug Error:', errorInfo)
      }
    }

    checkAuth()
  }, [user, loading])

  const testLogin = async () => {
    try {
      setTestResult('Testing login...')
      await signIn('<EMAIL>', 'admin123')
      setTestResult('Login successful!')
    } catch (error: any) {
      setTestResult(`Login failed: ${error.message}`)
      console.error('Login test error:', error)
    }
  }

  const testSupabaseConnection = async () => {
    try {
      setTestResult('Testing Supabase connection...')
      const { data, error } = await supabase.from('siswa').select('count').limit(1)
      if (error) {
        setTestResult(`Supabase connection failed: ${error.message}`)
      } else {
        setTestResult('Supabase connection successful!')
      }
    } catch (error: any) {
      setTestResult(`Supabase connection error: ${error.message}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Debug Info */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>

          {/* Test Actions */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
            
            <div className="space-y-4">
              <button
                onClick={testSupabaseConnection}
                className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Test Supabase Connection
              </button>
              
              <button
                onClick={testLogin}
                className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
              >
                Test Login (<EMAIL>)
              </button>
              
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
              >
                Go to Login Page
              </button>
              
              <button
                onClick={() => router.push('/')}
                className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
              >
                Go to Dashboard
              </button>
            </div>
            
            {testResult && (
              <div className="mt-4 p-4 bg-gray-100 rounded">
                <strong>Test Result:</strong>
                <p>{testResult}</p>
              </div>
            )}
          </div>
        </div>

        {/* Current Status */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Current Status</h2>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className={`p-4 rounded ${loading ? 'bg-yellow-100' : 'bg-gray-100'}`}>
              <div className="font-semibold">Loading</div>
              <div>{loading ? 'Yes' : 'No'}</div>
            </div>
            <div className={`p-4 rounded ${user ? 'bg-green-100' : 'bg-red-100'}`}>
              <div className="font-semibold">User</div>
              <div>{user ? 'Logged In' : 'Not Logged In'}</div>
            </div>
            <div className="p-4 rounded bg-blue-100">
              <div className="font-semibold">Email</div>
              <div>{user?.email || 'N/A'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
