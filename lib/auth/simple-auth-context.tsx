'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'

interface SimpleAuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  hasPermission: (module: string, action: string) => boolean
  hasRole: (roleName: string) => boolean
}

const SimpleAuthContext = createContext<SimpleAuthContextType | undefined>(undefined)

export function SimpleAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('SimpleAuthProvider - Initializing...')

    // Get initial session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      console.log('Simple auth - Initial session check:', {
        hasSession: !!session?.session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email,
        error: error?.message
      })
      setUser(session?.user ?? null)
      setLoading(false)
    }).catch((error) => {
      console.error('Simple auth - Initial session error:', error)
      setUser(null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Simple auth - Auth state change:', event, {
        hasSession: !!session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email
      })
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => {
      console.log('SimpleAuthProvider - Cleaning up subscription')
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  // For now, give admin permissions to all logged-in users
  const hasPermission = (module: string, action: string): boolean => {
    return !!user // If user is logged in, they have all permissions
  }

  const hasRole = (roleName: string): boolean => {
    return !!user // If user is logged in, they have all roles
  }

  const value: SimpleAuthContextType = {
    user,
    loading,
    signIn,
    signOut,
    hasPermission,
    hasRole,
  }

  return <SimpleAuthContext.Provider value={value}>{children}</SimpleAuthContext.Provider>
}

export function useSimpleAuth() {
  const context = useContext(SimpleAuthContext)
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider')
  }
  return context
}

// Simple HOC for protecting routes
export function withSimpleAuth<P extends object>(
  Component: React.ComponentType<P>
) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useSimpleAuth()
    const router = useRouter()

    useEffect(() => {
      if (typeof window !== 'undefined') {
        console.log('withSimpleAuth - Auth state:', {
          user: !!user,
          loading,
          pathname: window.location.pathname,
          shouldRedirect: !loading && !user && window.location.pathname !== '/login'
        })

        if (!loading && !user) {
          const currentPath = window.location.pathname
          if (currentPath !== '/login') {
            console.log('No user found, redirecting to login from:', currentPath)
            // Try both methods to ensure redirect works
            router.push('/login')
            // Fallback redirect after a short delay
            setTimeout(() => {
              if (window.location.pathname !== '/login') {
                console.log('Router.push failed, using window.location')
                window.location.href = '/login'
              }
            }, 100)
          }
        }
      }
    }, [user, loading, router])

    if (loading) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg flex items-center justify-center mb-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Dashboard Magang Jepang
            </h2>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      )
    }

    if (!user) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 bg-gradient-to-r from-orange-500 to-maroon-600 rounded-lg flex items-center justify-center mb-4">
              <div className="animate-pulse">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
              </div>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Dashboard Magang Jepang
            </h2>
            <p className="text-gray-600">Redirecting to login...</p>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}
