'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface MinimalAuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  hasPermission: (module: string, action: string) => boolean
  hasRole: (roleName: string) => boolean
}

const MinimalAuthContext = createContext<MinimalAuthContextType | undefined>(undefined)

export function MinimalAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('MinimalAuthProvider - Initializing...')
    
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        console.log('Minimal auth - Initial session check:', {
          hasSession: !!session?.session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email,
          error: error?.message
        })
        setUser(session?.user ?? null)
      } catch (error) {
        console.error('Minimal auth - Initial session error:', error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Minimal auth - Auth state change:', event, {
        hasSession: !!session,
        hasUser: !!session?.user,
        userEmail: session?.user?.email
      })
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => {
      console.log('MinimalAuthProvider - Cleaning up subscription')
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    if (error) throw error
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  // Simple permission system - all logged-in users have all permissions
  const hasPermission = (module: string, action: string): boolean => {
    return !!user
  }

  const hasRole = (roleName: string): boolean => {
    return !!user
  }

  const value: MinimalAuthContextType = {
    user,
    loading,
    signIn,
    signOut,
    hasPermission,
    hasRole,
  }

  return <MinimalAuthContext.Provider value={value}>{children}</MinimalAuthContext.Provider>
}

export function useMinimalAuth() {
  const context = useContext(MinimalAuthContext)
  if (context === undefined) {
    throw new Error('useMinimalAuth must be used within a MinimalAuthProvider')
  }
  return context
}
