'use client'

import { useEffect, useState } from 'react'
import { useSimpleAuth } from '@/lib/auth/simple-auth-context'
import { supabase } from '@/lib/supabase'

export function AuthDebug() {
  const { user, loading } = useSimpleAuth()
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: session, error } = await supabase.auth.getSession()
        setDebugInfo({
          hasUser: !!user,
          loading,
          sessionExists: !!session?.session,
          sessionError: error?.message,
          supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
          currentPath: window.location.pathname,
          timestamp: new Date().toISOString()
        })
      } catch (err: any) {
        setDebugInfo({
          error: err.message,
          timestamp: new Date().toISOString()
        })
      }
    }

    checkAuth()
  }, [user, loading])

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">Auth Debug Info</h3>
      <pre className="whitespace-pre-wrap">
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
    </div>
  )
}
